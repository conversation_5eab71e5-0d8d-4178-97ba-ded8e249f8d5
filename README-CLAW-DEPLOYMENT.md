# Claude Relay Service - Claw Cloud Run 部署版本

## 🌩️ Claw Cloud Run 部署指南

本文档提供了将 Claude Relay Service 部署到 Claw Cloud Run 平台的完整解决方案。

### 🎯 部署方案概述

- **平台**: Claw Cloud Run (Serverless 容器平台)
- **数据库**: Upstash Redis (外部托管)
- **成本**: 利用免费额度，小规模使用完全免费
- **特点**: 自动 HTTPS、实时日志、弹性扩容

### 📋 快速部署

#### 方法一：一键部署脚本

```bash
# 1. 下载并运行准备脚本
curl -O https://raw.githubusercontent.com/your-repo/scripts/prepare-claw-deployment.sh
chmod +x prepare-claw-deployment.sh
./prepare-claw-deployment.sh

# 2. 配置 Redis 连接信息
cd claude-relay-service
nano .env.claw

# 3. 构建和推送镜像
../scripts/build-and-push.sh your-claw-username

# 4. 在 Claw Cloud Run 控制台完成部署
```

#### 方法二：手动部署

详细步骤请参考：[完整部署指南](docs/claw-cloud-run-deployment.md)

### 🔧 核心配置

#### 必需环境变量
```bash
# Redis 配置 (Upstash)
REDIS_HOST=your-upstash-redis-host.upstash.io
REDIS_PORT=6379
REDIS_PASSWORD=your-upstash-redis-password

# 安全配置
JWT_SECRET=your-generated-32-char-secret
ENCRYPTION_KEY=your-generated-32-char-key

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password

# 服务配置
PORT=3000
NODE_ENV=production
```

### 💰 成本分析

| 服务 | 免费额度 | 预估成本 |
|------|----------|----------|
| Claw Cloud Run | $5/月 | 小规模免费 |
| Upstash Redis | 10K 请求/天 | 小规模免费 |
| **总计** | - | **基本免费** |

### 📁 项目结构

```
claude-relay-service/
├── .Planning/                    # 部署规划文档
│   ├── deployment-guide.md       # 详细部署指南
│   ├── environment-config.md     # 环境变量配置
│   ├── upstash-setup.md         # Upstash Redis 设置
│   └── scratchpad.md            # 分析记录
├── deploy/
│   └── claw-cloud-run.dockerfile # 优化的 Dockerfile
├── scripts/
│   ├── prepare-claw-deployment.sh # 部署准备脚本
│   └── build-and-push.sh         # 镜像构建脚本
├── docs/
│   └── claw-cloud-run-deployment.md # 完整部署文档
└── README-CLAW-DEPLOYMENT.md     # 本文件
```

### 🚀 部署流程

1. **准备阶段**
   - 注册 Claw Cloud 账号（GitHub 注册 > 180天）
   - 注册 Upstash Redis 免费账号
   - 安装 Docker 和 Git

2. **配置阶段**
   - 克隆项目代码
   - 生成安全密钥
   - 配置环境变量

3. **构建阶段**
   - 构建 Docker 镜像
   - 推送到 Claw Cloud 镜像仓库

4. **部署阶段**
   - 在 Claw Cloud Run 创建应用
   - 配置环境变量
   - 启动服务

5. **验证阶段**
   - 测试管理界面
   - 配置 Claude 账户
   - 创建 API Key

### 🛠️ 故障排除

#### 常见问题

**Redis 连接失败**
```bash
# 检查连接
redis-cli -h your-host -p 6379 -a your-password ping
```

**镜像推送失败**
```bash
# 重新登录
docker login registry.clawcloud.io
```

**服务启动失败**
- 查看 Claw Cloud Run 控制台日志
- 检查环境变量配置
- 验证端口设置 (3000)

### 📊 监控和维护

#### 性能监控
- Claw Cloud Run 控制台：CPU/内存使用
- Upstash Console：Redis 请求统计
- 应用管理界面：API 使用情况

#### 定期维护
- 检查服务状态
- 更新 Claude 账户授权
- 监控免费额度使用

### 🔒 安全建议

- 使用强随机密钥
- 定期轮换敏感信息
- 启用 HTTPS（自动配置）
- 监控异常访问

### 📞 获取支持

- **详细文档**: [docs/claw-cloud-run-deployment.md](docs/claw-cloud-run-deployment.md)
- **配置指南**: [.Planning/environment-config.md](.Planning/environment-config.md)
- **Redis 设置**: [.Planning/upstash-setup.md](.Planning/upstash-setup.md)
- **GitHub Issues**: 技术问题和建议

### 🎉 部署成功后

部署成功后，您将获得：
- 🌐 自动 HTTPS 域名：`https://your-app.claw.run`
- 🎛️ 管理界面：`https://your-app.claw.run/web`
- 🔌 API 端点：`https://your-app.claw.run/api/`

#### 客户端配置
```bash
export ANTHROPIC_BASE_URL="https://your-app.claw.run/api/"
export ANTHROPIC_AUTH_TOKEN="your-generated-api-key"
claude
```

---

**署名**: NEO JOU  
**部署方案版本**: v1.0.0  
**适用平台**: Claw Cloud Run + Upstash Redis  
**最后更新**: 2025-01-19

## 🙏 致谢

感谢以下项目和服务：
- [Claude Relay Service](https://github.com/Wei-Shaw/claude-relay-service) - 原始项目
- [Claw Cloud Run](https://run.claw.cloud/) - 部署平台
- [Upstash Redis](https://upstash.com/) - Redis 服务
